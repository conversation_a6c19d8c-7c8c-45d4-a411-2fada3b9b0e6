import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/json_consts.dart';
import '/src/core/config/constants.dart';
import '/src/core/config/responsive.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/theme/app_fonts.dart';
import 'month_year_picker.dart';

class CustomCalendarWidget extends HookWidget {
  final DateTime currentMonth;
  final String selectedQuickOption;
  final DateTime? selectedDate;
  final DateTime? rangeStart;
  final DateTime? rangeEnd;
  final bool isCustomRangeMode;
  final DateTime? customRangeStart;
  final DateTime? customRangeEnd;
  final Function(String) onQuickSelection;
  final Function(DateTime) onDateSelection;
  final Function(int) onNavigateMonth;
  final VoidCallback onCancel;
  final VoidCallback onApply;
  final bool Function(DateTime) isDateInSelectedRange;
  final bool Function(DateTime) isRangeStartDate;

  const CustomCalendarWidget({
    super.key,
    required this.currentMonth,
    required this.selectedQuickOption,
    required this.selectedDate,
    required this.rangeStart,
    required this.rangeEnd,
    required this.isCustomRangeMode,
    required this.customRangeStart,
    required this.customRangeEnd,
    required this.onQuickSelection,
    required this.onDateSelection,
    required this.onNavigateMonth,
    required this.onCancel,
    required this.onApply,
    required this.isDateInSelectedRange,
    required this.isRangeStartDate,
  });

  @override
  Widget build(BuildContext context) {
    final showMonthYearPicker = useState(false);

    // Use more aggressive mobile detection for calendar
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobileCalendar = screenWidth < 1000; // More aggressive mobile breakpoint for calendar

    return Stack(
      children: [
        if (isMobileCalendar)
          _buildMobileCalendar(context, showMonthYearPicker)
        else
          _buildDesktopCalendar(context, showMonthYearPicker),

        if (showMonthYearPicker.value)
          Positioned.fill(
            child: Container(
              color: AppTheme.black.withValues(alpha: 0.5),
              child: Center(
                child: MonthYearPicker(
                  initialDate: currentMonth,
                  onDateSelected: (DateTime newDate) {
                    onNavigateMonth(
                      newDate.month -
                          currentMonth.month +
                          (newDate.year - currentMonth.year) * 12,
                    );
                    showMonthYearPicker.value = false;
                  },
                  onCancel: () {
                    showMonthYearPicker.value = false;
                  },
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMobileCalendar(
    BuildContext context,
    ValueNotifier<bool> showPicker,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(defaultPadding * 0.8), // Increased padding
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMobileCalendarHeader(showPicker),
            _buildMobileQuickSelectionBubbles(),
            _buildMobileWeekdayHeaders(),
            _buildMobileDatesGrid(),
            _buildMobileActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopCalendar(
    BuildContext context,
    ValueNotifier<bool> showPicker,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildQuickSelectionSidebar(context),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.all(defaultPadding),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildCalendarHeader(showPicker),
                        _buildWeekdayHeaders(context),
                        Flexible(child: _buildDatesGrid(context)),
                        _buildActionButtons(context),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildQuickSelectionSidebar(BuildContext context) {
    final sidebarWidth = Responsive.isTablet(context) ? 140.0 : 160.0;

    return Container(
      width: sidebarWidth,
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(10),
          bottomLeft: Radius.circular(10),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(defaultPadding),
            child: Text(quickSelection, style: AppFonts.semiBoldTextStyle(14)),
          ),
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: quickSelectionOptions.length,
              itemBuilder: (context, index) {
                final option = quickSelectionOptions[index];
                final isSelected = selectedQuickOption == option;
                return _buildQuickOptionListItem(option, isSelected);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickOptionListItem(String option, bool isSelected) {
    return GestureDetector(
      onTap: () => onQuickSelection(option),
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 2,
          vertical: 2,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.selectedComboBoxBorder
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          option,
          style: AppFonts.regularTextStyle(
            12,
            color: isSelected ? AppTheme.white : AppTheme.black,
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarHeader(ValueNotifier<bool> showPicker) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () => onNavigateMonth(-1),
          icon: Icon(Icons.chevron_left, color: AppTheme.black),
        ),
        GestureDetector(
          onTap: () => showPicker.value = true,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                DateFormat('MMM yyyy').format(currentMonth),
                style: AppFonts.semiBoldTextStyle(16),
              ),
              SizedBox(width: defaultPadding / 4),
              Icon(
                Icons.calendar_month,
                size: 16,
                color: AppTheme.tableDataFont,
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => onNavigateMonth(1),
          icon: Icon(Icons.chevron_right, color: AppTheme.black),
        ),
      ],
    );
  }

  Widget _buildWeekdayHeaders(BuildContext context) {
    return Row(
      children: weekdayHeaders
          .map(
            (day) => Expanded(
              child: Center(
                child: Text(
                  day,
                  style: AppFonts.semiBoldTextStyle(
                    Responsive.isMobile(context) ? 14 : 12,
                    color: AppTheme.tableDataFont,
                  ),
                ),
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildDatesGrid(BuildContext context) {
    final daysInMonth = DateTime(
      currentMonth.year,
      currentMonth.month + 1,
      0,
    ).day;
    final firstWeekday = DateTime(
      currentMonth.year,
      currentMonth.month,
      1,
    ).weekday;
    final now = DateTime.now();

    // Calculate responsive grid dimensions
    final isMobile = Responsive.isMobile(context);
    final spacing = isMobile ? 4.0 : 3.0; // Reduced spacing

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
        mainAxisSpacing: spacing,
        crossAxisSpacing: spacing,
      ),
      itemCount: 42, // 6 weeks * 7 days
      itemBuilder: (context, index) {
        final dayNumber = index - firstWeekday + 2;
        final isValidDay = dayNumber > 0 && dayNumber <= daysInMonth;

        if (!isValidDay) {
          return const SizedBox();
        }

        final date = DateTime(
          currentMonth.year,
          currentMonth.month,
          dayNumber,
        );
        return _buildDateCell(date, now, context);
      },
    );
  }

  Widget _buildDateCell(DateTime date, DateTime now, BuildContext context) {
    final isSelected =
        selectedDate != null &&
        date.year == selectedDate!.year &&
        date.month == selectedDate!.month &&
        date.day == selectedDate!.day;

    final isToday =
        date.year == now.year && date.month == now.month && date.day == now.day;

    final isInRange = isDateInSelectedRange(date);
    final isStartDate = isRangeStartDate(date);
    final isCustomRangeStart =
        customRangeStart != null &&
        date.year == customRangeStart!.year &&
        date.month == customRangeStart!.month &&
        date.day == customRangeStart!.day;
    final isCustomRangeEnd =
        customRangeEnd != null &&
        date.year == customRangeEnd!.year &&
        date.month == customRangeEnd!.month &&
        date.day == customRangeEnd!.day;

    Color? bgColor;
    Color textColor = AppTheme.black;

    if (isSelected || isStartDate || isCustomRangeStart || isCustomRangeEnd) {
      bgColor = AppTheme.selectedComboBoxBorder;
      textColor = AppTheme.white;
    } else if (isInRange) {
      bgColor = AppTheme.searchbarBg;
      textColor = AppTheme.black;
    } else if (isToday) {
      bgColor = AppTheme.selectedComboBoxBorder.withValues(alpha: 0.2);
      textColor = AppTheme.selectedComboBoxBorder;
    }

    return GestureDetector(
      onTap: () => onDateSelection(date),
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(8),
          border: isToday && !isSelected && !isStartDate
              ? Border.all(color: AppTheme.selectedComboBoxBorder, width: 1)
              : null,
        ),
        child: Center(
          child: Text(
            date.day.toString(),
            style:
                AppFonts.regularTextStyle(
                  Responsive.isMobile(context) ? 14 : 12,
                  color: textColor,
                ).copyWith(
                  fontWeight: isSelected || isToday || isStartDate
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final isMobile = Responsive.isMobile(context);

    return Padding(
      padding: EdgeInsets.only(
        top: isMobile ? defaultPadding * 0.4 : defaultPadding * 0.3, // Reduced top spacing
        left: isMobile ? defaultPadding / 2 : defaultPadding,
        right: isMobile ? defaultPadding / 2 : defaultPadding,
        bottom: isMobile ? defaultPadding / 2 : defaultPadding,
      ),
      child: Row(
        children: [
          Expanded(child: _buildSelectedDisplayButton()),
          SizedBox(width: isMobile ? defaultPadding / 4 : defaultPadding / 2),
          _buildCancelButton(),
          SizedBox(width: isMobile ? defaultPadding / 4 : defaultPadding / 4),
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildSelectedDisplayButton() {
    String displayText = _getDisplayText();

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding / 2,
        vertical: defaultPadding / 4,
      ),
      child: Text(
        displayText,
        style: AppFonts.regularTextStyle(10, color: AppTheme.tableDataFont),
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildCancelButton() {
    return GestureDetector(
      onTap: onCancel,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.comboBoxBorder),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text('Cancel', style: AppFonts.regularTextStyle(12)),
      ),
    );
  }

  Widget _buildApplyButton() {
    return GestureDetector(
      onTap: onApply,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: AppTheme.selectedComboBoxBorder,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'Apply',
          style: AppFonts.regularTextStyle(12, color: AppTheme.white),
        ),
      ),
    );
  }

  String _getDisplayText() {
    if (selectedDate != null) {
      return _formatDateRange(selectedDate, selectedDate);
    } else if (isCustomRangeMode) {
      return _formatDateRange(customRangeStart, customRangeEnd);
    } else if (rangeStart != null &&
        rangeEnd != null &&
        selectedQuickOption.isNotEmpty) {
      return _formatDateRange(rangeStart, rangeEnd);
    } else {
      return 'Select date range';
    }
  }

  String _formatDateRange(DateTime? start, DateTime? end) {
    if (start == null && end == null) {
      return 'Select date range';
    }

    final formatter = DateFormat('dd/MM/yyyy');

    if (start != null && end != null) {
      if (start.isAtSameMomentAs(end)) {
        return formatter.format(start);
      }
      return '${formatter.format(start)} - ${formatter.format(end)}';
    } else if (start != null) {
      return formatter.format(start);
    } else if (end != null) {
      return formatter.format(end);
    }

    return 'Select date range';
  }

  // Mobile methods - compact design
  Widget _buildMobileQuickSelectionBubbles() {
    return Container(
      height: 44, // Taller for better touch targets
      margin: EdgeInsets.only(bottom: defaultPadding * 0.6),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(), // Better scroll physics
        padding: EdgeInsets.symmetric(horizontal: defaultPadding * 0.3),
        itemCount: quickSelectionOptions.length,
        itemBuilder: (context, index) {
          final option = quickSelectionOptions[index];
          final isSelected = selectedQuickOption == option;
          return _buildMobileQuickOptionBubble(option, isSelected);
        },
      ),
    );
  }

  Widget _buildMobileQuickOptionBubble(String option, bool isSelected) {
    return Padding(
      padding: EdgeInsets.only(right: defaultPadding * 0.4),
      child: Center(
        child: GestureDetector(
          onTap: () => onQuickSelection(option),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: defaultPadding * 0.8,
              vertical: defaultPadding * 0.4,
            ),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppTheme.selectedComboBoxBorder
                  : AppTheme.white,
              borderRadius: BorderRadius.circular(20), // Bubble style
              border: Border.all(
                color: isSelected
                    ? AppTheme.selectedComboBoxBorder
                    : AppTheme.comboBoxBorder,
              ),
            ),
            child: Center(
              child: Text(
                option,
                style: AppFonts.regularTextStyle(
                  12, // Increased font size
                  color: isSelected ? AppTheme.white : AppTheme.black,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileCalendarHeader(ValueNotifier<bool> showPicker) {
    return Padding(
      padding: EdgeInsets.only(bottom: defaultPadding * 0.4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => onNavigateMonth(-1),
            child: Container(
              padding: EdgeInsets.all(defaultPadding * 0.6),
              decoration: BoxDecoration(
                color: AppTheme.searchbarBg,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.chevron_left,
                size: 14,
                color: AppTheme.black.withValues(alpha: 0.7),
              ),
            ),
          ),
          GestureDetector(
            onTap: () => showPicker.value = true,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  DateFormat('MMMM yyyy').format(currentMonth),
                  style: AppFonts.semiBoldTextStyle(16), // Increased font size
                ),
                SizedBox(width: defaultPadding * 0.2),
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: AppTheme.black.withValues(alpha: 0.7),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () => onNavigateMonth(1),
            child: Container(
              padding: EdgeInsets.all(defaultPadding * 0.6),
              decoration: BoxDecoration(
                color: AppTheme.searchbarBg,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.chevron_right,
                size: 14,
                color: AppTheme.black.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileWeekdayHeaders() {
    return Padding(
      padding: EdgeInsets.only(bottom: defaultPadding * 0.4),
      child: Row(
        children: weekdayHeaders
            .map(
              (day) => Expanded(
                child: Center(
                  child: Text(
                    day,
                    style: AppFonts.semiBoldTextStyle(
                      12, // Increased font size
                      color: AppTheme.tableDataFont,
                    ),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildMobileDatesGrid() {
    final daysInMonth = DateTime(
      currentMonth.year,
      currentMonth.month + 1,
      0,
    ).day;
    final firstWeekday = DateTime(
      currentMonth.year,
      currentMonth.month,
      1,
    ).weekday;
    final now = DateTime.now();

    // Calculate grid height for mobile
    final cellSize = 42.0; // Increased cell size
    final spacing = 4.0; // Increased spacing
    final gridHeight = cellSize * 6 + (spacing * 5); // 6 rows + spacing

    return SizedBox(
      height: gridHeight,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1.0,
          mainAxisSpacing: spacing,
          crossAxisSpacing: spacing,
        ),
        itemCount: 42,
        itemBuilder: (context, index) {
          final dayNumber = index - firstWeekday + 2;
          final isValidDay = dayNumber > 0 && dayNumber <= daysInMonth;

          if (!isValidDay) {
            return const SizedBox();
          }

          final date = DateTime(currentMonth.year, currentMonth.month, dayNumber);
          return _buildMobileDateCell(date, now);
        },
      ),
    );
  }

  Widget _buildMobileDateCell(DateTime date, DateTime now) {
    final isSelected =
        selectedDate != null &&
        date.year == selectedDate!.year &&
        date.month == selectedDate!.month &&
        date.day == selectedDate!.day;

    final isToday =
        date.year == now.year && date.month == now.month && date.day == now.day;

    final isInRange = isDateInSelectedRange(date);
    final isStartDate = isRangeStartDate(date);
    final isCustomRangeStart =
        customRangeStart != null &&
        date.year == customRangeStart!.year &&
        date.month == customRangeStart!.month &&
        date.day == customRangeStart!.day;
    final isCustomRangeEnd =
        customRangeEnd != null &&
        date.year == customRangeEnd!.year &&
        date.month == customRangeEnd!.month &&
        date.day == customRangeEnd!.day;

    Color? bgColor;
    Color textColor = AppTheme.black;

    if (isSelected || isStartDate || isCustomRangeStart || isCustomRangeEnd) {
      bgColor = AppTheme.selectedComboBoxBorder;
      textColor = AppTheme.white;
    } else if (isInRange) {
      bgColor = AppTheme.searchbarBg;
      textColor = AppTheme.black;
    } else if (isToday) {
      bgColor = AppTheme.selectedComboBoxBorder.withValues(alpha: 0.2);
      textColor = AppTheme.selectedComboBoxBorder;
    }

    return GestureDetector(
      onTap: () => onDateSelection(date),
      child: Container(
        margin: EdgeInsets.all(1.0), // Slightly larger margin
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(8), // Larger radius
          border: isToday && !isSelected && !isStartDate
              ? Border.all(color: AppTheme.selectedComboBoxBorder, width: 1)
              : null,
        ),
        child: Center(
          child: Text(
            date.day.toString(),
            style:
                AppFonts.regularTextStyle(
                  14, // Larger font for mobile
                  color: textColor,
                ).copyWith(
                  fontWeight: isSelected || isToday || isStartDate
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileActionButtons() {
    return Padding(
      padding: EdgeInsets.only(
        top: defaultPadding * 0.8, // More spacing from calendar
        left: defaultPadding * 0.3,
        right: defaultPadding * 0.3,
        bottom: defaultPadding * 0.6,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Full width selected date display button
          _buildMobileSelectedDisplayButton(),
          SizedBox(height: defaultPadding * 0.8), // More spacing between sections
          // Cancel and Apply buttons in a row
          Row(
            children: [
              Expanded(child: _buildMobileCancelButton()),
              SizedBox(width: defaultPadding * 0.6),
              Expanded(child: _buildMobileApplyButton()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMobileSelectedDisplayButton() {
    String displayText = _getDisplayText();

    return Container(
      width: double.infinity, // Ensure full width
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 0.8,
        vertical: defaultPadding * 0.6,
      ),
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.comboBoxBorder.withValues(alpha: 0.5)),
      ),
      child: Text(
        displayText,
        style: AppFonts.regularTextStyle(
          13, // Slightly larger font
          color: AppTheme.tableDataFont,
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildMobileCancelButton() {
    return GestureDetector(
      onTap: onCancel,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding * 0.6,
          vertical: defaultPadding * 0.5,
        ),
        decoration: BoxDecoration(
          color: AppTheme.searchbarBg,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.comboBoxBorder),
        ),
        child: Center(
          child: Text(
            'Cancel',
            style: AppFonts.regularTextStyle(
              13,
              color: AppTheme.black,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileApplyButton() {
    return GestureDetector(
      onTap: onApply,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding * 0.6,
          vertical: defaultPadding * 0.5,
        ),
        decoration: BoxDecoration(
          color: AppTheme.selectedComboBoxBorder,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            'Apply',
            style: AppFonts.regularTextStyle(
              13,
              color: AppTheme.white,
            ),
          ),
        ),
      ),
    );
  }
}

